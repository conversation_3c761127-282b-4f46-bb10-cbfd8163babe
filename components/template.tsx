// components/template.tsx

"use client";
import type React from "react";
import { Router, Voucher } from "@/components/icons/list";
import { useState, useEffect, useRef } from "react";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import { useAuth } from "@/context/AuthContext";
import {
  LayoutDashboard,
  Menu,
  Settings,
  Package2,
  User,
  UsersRound,
  UserRound,
  LogOut,
  Group,
  Chart,
  Admin,
} from "@/components/icons/list";

import { Button } from "@/components/ui/button";
import Link from "next/link";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";
import jwt from "jsonwebtoken";

interface TemplateProps {
  children: React.ReactNode;
}

export default function Template({ children }: TemplateProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false); // Controls collapsed state for both mobile/desktop
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isStaffsOpen, setIsStaffsOpen] = useState(false);
  const { logout } = useAuth();
  const [user, setUser] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const accessToken = Cookies.get("accessToken");
  const decodedToken = jwt.decode(accessToken) as any;
  const user_id = decodedToken?.user_id;

  const fetchUser = async () => {
    try {
      const response = await apiClient.get(`/user/?id=${user_id}`);
      setUser(response?.data);
    } catch (error) {
      console.error("Failed to fetch user:", error);
    }
  };

  useEffect(() => {
    fetchUser();
  }, []);

  // --- Start of new loading animation logic ---
  const startDelayedLoading = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
    }
    loadingTimeoutRef.current = setTimeout(() => {
      setIsLoading(true);
    }, 200);
  };

  const stopLoading = () => {
    if (loadingTimeoutRef.current) {
      clearTimeout(loadingTimeoutRef.current);
      loadingTimeoutRef.current = null;
    }
    setIsLoading(false);
  };

  useEffect(() => {
    stopLoading(); // Hide spinner when pathname changes (new page loaded)
    // No need to close a mobile sidebar if it's always collapsed
  }, [pathname]);

  // --- End of new loading animation logic ---

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    startDelayedLoading();
    try {
      await logout();
      console.log("Logout successful");
      router.push("/");
    } catch (err: any) {
      console.error("Logout failed", err);
    } finally {
      stopLoading();
    }
  };

  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    // Set initial window width
    setWindowWidth(window.innerWidth);

    // Set initial sidebar state based on screen size
    if (window.innerWidth < 768) {
      // Assuming 768px is your mobile breakpoint (Tailwind's md)
      setIsSidebarCollapsed(true); // Collapsed on mobile
    } else {
      setIsSidebarCollapsed(false); // Open on desktop initially
    }

    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      if (window.innerWidth < 768) {
        setIsSidebarCollapsed(true); // Keep collapsed on resize to mobile
      } else {
        // On desktop, keep its previous state (don't force open/close)
        // If you want it to always open on desktop resize, uncomment the line below:
        setIsSidebarCollapsed(false);
      }
    };

    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []); // Empty dependency array means this runs once on mount and cleanup on unmount

  const closeAllMenus = (keep?: "staff" | "settings") => {
    if (keep !== "staff") setIsStaffsOpen(false);
    if (keep !== "settings") setIsSettingsOpen(false);
  };




  return (
    <div className="flex h-screen bg-gray-200">
      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[9999]">
          <div className="flex space-x-2">
            <div className="w-3 h-3 bg-white rounded-full animate-bounce-slow"></div>
            <div className="w-3 h-3 bg-white rounded-full animate-bounce-medium"></div>
            <div className="w-3 h-3 bg-white rounded-full animate-bounce-fast"></div>
          </div>
        </div>
      )}

      {/* Sidebar */}
      <div
        className={`bg-primary text-white transition-all duration-300 flex flex-col flex-shrink-0
          ${isSidebarCollapsed ? "w-[50px]" : "w-[170px] overflow-hidden"}
          ${windowWidth < 768 ? "md:w-[170px]" : ""
          } // Ensure desktop width is applied above md breakpoint
        `}
      >
        <div className="p-2 flex items-center justify-center">
          <div className="relative h-2 w-full flex items-center justify-center mt-3">
            <Image
              src="/images/nepal_police_small.png"
              href="/app"
              alt="nepal police logo"
              width={30}
              height={30}
              className={`absolute transition-opacity duration-300 text-[10px] ${isSidebarCollapsed ? "opacity-100" : "opacity-0"
                }`}
            />
            <Image
              src="/images/nepal_police.png"
              href="/app"
              alt="nepal police logo"
              width={100}
              height={30}
              className={`absolute transition-opacity duration-300 text-[10px] ${isSidebarCollapsed ? "opacity-0" : "opacity-100"
                }`}
            />
          </div>
        </div>

        <nav className="mt-3 flex flex-col h-[calc(100vh-80px)] md:flex overflow-y-auto no-scrollbar">
          <SidebarItem
            icon={<LayoutDashboard />}
            text="Dashboard"
            active={pathname === "/app"}
            collapsed={isSidebarCollapsed}
            href="/app"
            onClick={() => closeAllMenus()}
            startDelayedLoading={startDelayedLoading}
          />

          <SidebarItem
            icon={<Package2 />}
            text="Department"
            active={pathname === "/app/department"}
            collapsed={isSidebarCollapsed}
            href="/app/department"
            onClick={() => closeAllMenus()}
            startDelayedLoading={startDelayedLoading}
          />

          <SidebarItem
            icon={<UsersRound />}
            text="Staff"
            active={pathname === "/app/staff"}
            collapsed={isSidebarCollapsed}
            href="/app/staff"
            onClick={() => {
              closeAllMenus("staff");
              setIsStaffsOpen(prev => !prev);
            }}
            startDelayedLoading={startDelayedLoading}
          />

          <div
            className={`transition-all duration-300 ease-in-out ${isStaffsOpen ? "max-h-auto" : "visibility: hidden"
              }${!isSidebarCollapsed ? "ml-2" : "ml-0"}`}
          >
            <SidebarItem
              icon={<User />}
              text="External Staff"
              active={pathname === "/app/staff/external"}
              collapsed={isSidebarCollapsed}
              href="/app/staff/external"
              startDelayedLoading={startDelayedLoading}
            />

          </div>
          <SidebarItem
            icon={<Voucher />}
            text="Guest"
            active={pathname === "/app/guest"}
            collapsed={isSidebarCollapsed}
            href="/app/guest"
            onClick={() => closeAllMenus()}
            startDelayedLoading={startDelayedLoading}
          />

          <SidebarItem
            icon={<Chart />}
            text="Analytics"
            active={pathname === "/app/analytics"}
            collapsed={isSidebarCollapsed}
            href="/app/analytics"
            onClick={() => closeAllMenus()}
            startDelayedLoading={startDelayedLoading}
          />

          <SidebarItem
            icon={<Settings />}
            text="Settings"
            active={pathname === "/app/settings"}
            collapsed={isSidebarCollapsed}
            href="/app/settings"
            onClick={() => {
              closeAllMenus("settings");
              setIsSettingsOpen(prev => !prev);
            }}
            ariaExpanded={isSettingsOpen}
            startDelayedLoading={startDelayedLoading}
          />

          <div
            className={`transition-all duration-300 ease-in-out ${isSettingsOpen ? "max-h-auto" : "visibility: hidden"
              } ${!isSidebarCollapsed ? "ml-2" : "ml-0"}`}
          >
            <SidebarItem
              icon={<User />}
              text="Users"
              active={pathname === "/app/settings/users"}
              collapsed={isSidebarCollapsed}
              href="/app/settings/users"
              startDelayedLoading={startDelayedLoading}
            />

            <SidebarItem
              icon={<Group />}
              text="Groups"
              active={pathname === "/app/settings/groups"}
              collapsed={isSidebarCollapsed}
              href="/app/settings/groups"
              startDelayedLoading={startDelayedLoading}
            />

            <SidebarItem
              icon={<Router />}
              text="Devices"
              active={pathname === "/app/settings/devices"}
              collapsed={isSidebarCollapsed}
              href="/app/settings/devices"
              startDelayedLoading={startDelayedLoading}
            />

            <SidebarItem
              icon={<Admin />}
              text="Admin"
              active={pathname === "/app/settings/admin"}
              collapsed={isSidebarCollapsed}
              href="/app/settings/admin"
              startDelayedLoading={startDelayedLoading}
            />
          </div>
        </nav>

        <div className="mt-auto">
          <div className="relative h-2 w-full flex items-center justify-center border-t border-gray-800 py-6">
            <Image
              src="/images/workalaya-icon.png"
              href="/app"
              alt="nepal police logo"
              width={30}
              height={30}
              className={`absolute transition-opacity duration-300 text-xs ${isSidebarCollapsed ? "opacity-30" : "opacity-0"
                }`}
            />
            {/* <div
            className={`border-t border-gray-800 py-4 px-4 text-xs text-gray-400 transition-opacity duration-300 text-center flex justify-center ${
              isSidebarCollapsed ? "opacity-0" : "opacity-100"
            }`}
          > */}
            <span
              className={` text-xs text-gray-500 transition-opacity duration-300 text-center flex justify-center ${isSidebarCollapsed ? "opacity-0" : "opacity-100"
                }`}
            >
              © 2025 Workalaya <br />
              All Rights Reserved
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden ">
        {/* Header */}
        <header className="bg-grey-200 shadow-md flex items-center justify-between p-1">
          {/* Always show the menu button, and it always toggles `isSidebarCollapsed` */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
            className="mr-2"
          >
            <Menu />
          </Button>

          {/* <div className="flex-1 mx-0 relative ">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500" />
            <Input
              type="search"
              placeholder="Search..."
              className="max-w-[200px] h-9 bg-gray-50 pl-9 pr-3"
            />
          </div> */}

          <div className="flex items-center gap-2 mr-5">
            <div className="relative group">
              <button onClick={handleSubmit}>
                <div className="w-8 h-8 mr-5 rounded-full bg-gray-200 flex items-center justify-center cursor-pointer hover:bg-gray-300 transition overflow-hidden">
                  <LogOut className="h-4 w-4 text-gray-900" />
                </div>
              </button>

              <div className="absolute top-full left-1/3 -translate-x-1/2 mt-2 py-1.5 px-3 bg-[#222] rounded text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 flex items-center">
                <div className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 w-2 h-2 bg-[#222] rotate-45"></div>
                Logout
              </div>
            </div>
            <span className="text-sm hidden sm:block">
              Hello, {user?.username}!
            </span>

            <Link
              href="/app/userprofile"
              onClick={(e) => {
                if (pathname !== "/app/userprofile") {
                  startDelayedLoading();
                }
              }}
            >
              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center cursor-pointer hover:bg-gray-400 transition overflow-hidden">
                {user?.profilePic ? (
                  <Image
                    src={user?.profilePic}
                    alt="Profile Picture"
                    width={32}
                    height={32}
                    className="rounded-full object-cover w-7 h-7"
                  />
                ) : (
                  <UserRound className="h-5 w-5 text-gray-600" />
                )}
              </div>
            </Link>
          </div>
        </header>

        {/* Page Content */}
        <main className={`flex-1 overflow-y-auto p-0`}>{children}</main>
      </div>
    </div>
  );
}

// SidebarItem remains the same
interface SidebarItemProps {
  icon: React.ReactNode;
  text: string;
  active?: boolean;
  collapsed: boolean;
  href: string;
  onClick?: () => void;
  ariaExpanded?: boolean;
  startDelayedLoading?: () => void;
}

function SidebarItem({
  icon,
  text,
  active,
  collapsed,
  href,
  onClick,
  ariaExpanded,
  startDelayedLoading,
}: SidebarItemProps) {
  const pathname = usePathname();

  const handleNavigation = (e: React.MouseEvent) => {
    if (startDelayedLoading && href && href !== "#" && pathname !== href) {
      startDelayedLoading();
    }

    if (onClick) {
      onClick();
    }
  };

  return (
    <div className="relative group p-1">
      <Link href={href} onClick={handleNavigation} aria-expanded={ariaExpanded}>
        <div
          className={`flex items-center gap-2 rounded-full px-2 py-1.5 cursor-pointer transition-all duration-200 ease-in-out
        ${collapsed ? "justify-center w-9 h-9 mx-auto" : "px-4"}
        ${active ? "bg-blue-900" : ""}
        ${collapsed
              ? "hover:bg-[#1a2c3f] hover:rounded-full"
              : "hover:bg-[#1a2c3f]"
            }
      `}
        >
          {icon}
          {!collapsed && <span className="text-sm">{text}</span>}
        </div>
      </Link>

      {/* Tooltip that appears on hover when sidebar is collapsed */}
      {collapsed && (
        <div className="absolute left-full top-1/2 -translate-y-1/2 ml-2 py-1.5 px-3 bg-[#222] rounded text-white text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 flex items-center">
          <div className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1 w-2 h-2 bg-[#222] rotate-45"></div>
          {text}
        </div>
      )}
    </div>
  );
}
